import { useContext } from "react";
import { Button, Text, View } from "react-native";
import { AppContext } from "../../context/AppContext";

const Counter = () => {
    const { state, dispatch } = useContext(AppContext);

    const Add = () => {
        dispatch({ type: 'Add', payload: 1 });
    };

    return (
        <View>
            <Text>Count: {state.Counter}</Text>
            <Button title="Increment" onPress={Add} />
        </View>
    );
};

export default Counter;