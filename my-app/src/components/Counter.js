import { useContext } from "react";
import { Button, Text, View } from "react-native";
import { AppContext } from "../../context/AppContext";

const Counter = () => {
    const { state, dispatch } = useContext(AppContext);

    const increment = () => {
        dispatch({ type: 'INCREMENT', payload: 1 });
    };

    const decrement = () => {
        dispatch({ type: 'DECREMENT' , payload: 1 });
    };

    return (
        <View>
            <Text>Count: {state.Counter}</Text>
            <Button title="Increment" onPress={increment} />
            <Button title="Decrement" onPress={decrement} />
        </View>
    );
};

export default Counter;