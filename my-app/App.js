import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View } from 'react-native';
import Counter from './src/components/Counter';
import AppContextProvider from './context/AppContext';

export default function App() {
  return (
    
      <View style={styles.container}>
      
      <AppContextProvider>
         <Counter />
      </AppContextProvider>
       
      </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
