import React, { createContext, useReducer } from 'react';

export const AppContext = createContext();

// Initial state
const initialState = {
    Counter: 0
};

// Reducer function
const appReducer = (state, action) => {
    switch (action.type) {
        case 'INCREMENT':
            return {
                ...state,
                Counter: state.Counter + action.payload
            };
        case 'DECREMENT':
            return {
                ...state,
                Counter: state.Counter - action.payload
            };
        case 'RESET':
            return {
                ...state,
                Counter: 0
            };
        default:
            return state;
    }
};

const AppContextProvider = ({ children }) => {
    const [state, dispatch] = useReducer(appReducer, initialState);

    return (
        <AppContext.Provider value={{ state, dispatch }}>
            {children}
        </AppContext.Provider>
    );
};
export default AppContextProvider;